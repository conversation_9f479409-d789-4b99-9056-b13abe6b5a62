"""
金属电子逸出功实验模块
"""

from flask import render_template, redirect, url_for
import numpy as np
from scipy import stats
from .plotting_utils import init_plotting, get_current_labels, create_figure, save_figure_to_base64

class WorkFunctionExperiment:
    def __init__(self):
        """初始化逸出功实验"""
        # 初始化绘图环境
        self.use_chinese = init_plotting()
        
        # 实验常量
        self.temperatures = [1800, 1880, 1960, 2040, 2120]  # 开尔文
        self.currents = [0.55, 0.60, 0.65, 0.70, 0.75]  # 安培
        self.voltages = [16, 25, 36, 49, 64, 81, 100, 121]  # 伏特
        
    def index(self):
        """实验首页"""
        return render_template('work_function/index.html')
    
    def theory(self):
        """实验原理页面"""
        return render_template('work_function/theory.html')
    
    def steps(self):
        """实验步骤页面"""
        return render_template('work_function/steps.html')
    
    def input_data(self, step):
        """数据输入页面"""
        if step < 1 or step > 5:
            return redirect(url_for('work_function_index'))
        
        current_temp_index = step - 1
        current_temp = self.temperatures[current_temp_index]
        current_current = self.currents[current_temp_index]
        
        return render_template('work_function/input.html',
                             step=step,
                             current_temp=current_temp,
                             current_current=current_current,
                             voltages=self.voltages,
                             total_steps=5)
    
    def save_data(self, step, form_data, session):
        """保存实验数据"""
        if step < 1 or step > 5:
            return redirect(url_for('work_function_index'))
        
        # 初始化session数据
        if 'experiment_data' not in session:
            session['experiment_data'] = {}
        
        # 保存当前步骤的数据
        step_data = []
        for i, voltage in enumerate(self.voltages):
            value = form_data.get(f'current_{i}', '')
            if value.strip():
                try:
                    step_data.append(float(value))
                except ValueError:
                    step_data.append(self._generate_simulated_current(step-1, i))
            else:
                step_data.append(self._generate_simulated_current(step-1, i))
        
        session['experiment_data'][f'step_{step}'] = step_data
        session.modified = True
        
        # 如果是最后一步，跳转到结果页面
        if step == 5:
            return redirect(url_for('work_function_results'))
        else:
            return redirect(url_for('work_function_input', step=step+1))
    
    def results(self, session):
        """结果展示页面"""
        if 'experiment_data' not in session:
            return redirect(url_for('work_function_index'))
        
        # 重构数据格式
        measured_currents = []
        for i in range(1, 6):
            step_key = f'step_{i}'
            if step_key in session['experiment_data']:
                measured_currents.append(session['experiment_data'][step_key])
            else:
                # 如果某步数据缺失，生成模拟数据
                step_data = [self._generate_simulated_current(i-1, j) for j in range(8)]
                measured_currents.append(step_data)
        
        # 数据处理
        results = self._process_data(self.temperatures, self.voltages, measured_currents)
        return render_template('work_function/results.html', results=results)
    
    def reset(self, session):
        """重置实验数据"""
        session.pop('experiment_data', None)
        return redirect(url_for('work_function_index'))
    
    def _generate_simulated_current(self, temp_index, voltage_index):
        """生成模拟电流数据"""
        k = 8.617e-5  # 玻尔兹曼常数，eV/K
        work_function = 4.5  # 假设的逸出功，eV
        A = 1.2e6  # Richardson常数
        temp = self.temperatures[temp_index]
        voltage = self.voltages[voltage_index]
        
        base_current = A * (temp ** 2) * np.exp(-work_function / (k * temp))
        schottky_factor = np.exp(0.04 * np.sqrt(voltage) / (k * temp))
        simulated_current = base_current * schottky_factor * (1 + 0.05 * np.random.randn())
        return simulated_current
    
    def _process_data(self, temperatures, voltages, measured_currents):
        """处理实验数据并生成图表"""
        results = {}
        results['temperatures'] = temperatures
        results['voltages'] = voltages
        results['currents'] = measured_currents
        results['temp_count'] = len(temperatures)
        results['voltage_count'] = len(voltages)
        
        # 获取标签
        labels = get_current_labels()
        
        # 1. 绘制I-U特性曲线
        fig = create_figure(figsize=(10, 6))
        ax = fig.add_subplot(111)
        
        for i, temp in enumerate(temperatures):
            ax.plot(voltages, measured_currents[i], 'o-', label=labels['temp_k'].format(temp))
        
        ax.set_xlabel(labels['voltage'])
        ax.set_ylabel(labels['current'])
        ax.set_title(labels['iu_title'])
        ax.legend()
        ax.grid(True)
        
        results['iu_plot'] = save_figure_to_base64(fig)
        
        # 2. logI与sqrt(U)的线性拟合
        fig = create_figure(figsize=(10, 6))
        ax = fig.add_subplot(111)
        
        sqrt_voltages = np.sqrt(voltages)
        logu_fits = []
        
        for i, temp in enumerate(temperatures):
            currents = measured_currents[i]
            log_currents = np.log10(currents)
            
            # 线性拟合
            slope, intercept, r_value, _, _ = stats.linregress(sqrt_voltages, log_currents)
            
            # 绘制数据点和拟合线
            ax.plot(sqrt_voltages, log_currents, 'o', label=f'{labels["temp_k"].format(temp)} (data)')
            ax.plot(sqrt_voltages, slope * sqrt_voltages + intercept, '-',
                    label=f'{labels["temp_k"].format(temp)} (fit: y={slope:.4f}x+{intercept:.4f})')
            
            logu_fits.append({
                'temp': f"{temp}",
                'slope': f"{slope:.4f}",
                'intercept': f"{intercept:.4f}",
                'r_squared': f"{r_value ** 2:.4f}"
            })
        
        ax.set_xlabel(labels['sqrt_voltage'])
        ax.set_ylabel(labels['log_current'])
        ax.set_title(labels['schottky_title'])
        ax.legend()
        ax.grid(True)
        
        results['logu_plot'] = save_figure_to_base64(fig)
        results['logu_fits'] = logu_fits
        
        # 3. lg(I/T²)与1/T的线性拟合
        results.update(self._create_richardson_plot(temperatures, voltages, measured_currents, labels))

        return results

    def _create_richardson_plot(self, temperatures, voltages, measured_currents, labels):
        """创建Richardson拟合图"""
        fig = create_figure(figsize=(10, 6))
        ax = fig.add_subplot(111)

        # 选择一个固定的电压进行分析
        mid_index = len(voltages) // 2
        voltage_index = mid_index
        fixed_voltage = voltages[voltage_index]

        # 准备数据
        log_i_t2_values = []
        inv_temp_values = []
        temp_data = []

        for i, temp in enumerate(temperatures):
            current = measured_currents[i][voltage_index]
            log_i_t2 = np.log10(current / (temp ** 2))
            inv_temp = 1 / temp

            log_i_t2_values.append(log_i_t2)
            inv_temp_values.append(inv_temp)

            temp_data.append({
                'temp': f"{temp}",
                'inv_temp': f"{inv_temp:.7f}",
                'current': f"{current:.6e}",
                'log_i_t2': f"{log_i_t2:.6f}"
            })

        # 线性拟合
        slope2, intercept2, r_value2, _, _ = stats.linregress(inv_temp_values, log_i_t2_values)

        # 绘制数据点和拟合线
        ax.plot(inv_temp_values, log_i_t2_values, 'o', label=labels['data_label'])
        ax.plot(inv_temp_values, slope2 * np.array(inv_temp_values) + intercept2, 'r-',
                label=labels['fit_label'].format(slope2, intercept2))

        ax.set_xlabel(labels['inv_temp'])
        ax.set_ylabel(labels['log_i_t2'])
        ax.set_title(f'{labels["richardson_title"]} ({labels["voltage_fixed"].format(fixed_voltage)})')
        ax.legend()
        ax.grid(True)

        temp_plot = save_figure_to_base64(fig)

        # 计算电子逸出功
        k = 8.617e-5  # 玻尔兹曼常数，eV/K
        ln10 = np.log(10)
        work_function = -slope2 * k * ln10

        return {
            'temp_plot': temp_plot,
            'slope2': f"{slope2:.4f}",
            'intercept2': f"{intercept2:.4f}",
            'r_squared2': f"{r_value2 ** 2:.4f}",
            'work_function': f"{work_function:.4f}",
            'temp_data': temp_data
        }
