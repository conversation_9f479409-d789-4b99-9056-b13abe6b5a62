"""
绘图工具模块 - 解决中文字体显示问题
"""

import matplotlib
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import warnings

# 设置matplotlib使用非GUI后端
matplotlib.use('Agg')

def setup_chinese_font():
    """
    设置matplotlib支持中文字体
    """
    try:
        # 尝试设置中文字体
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
        
        # 验证字体设置
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        
        found_font = None
        for font in chinese_fonts:
            if font in available_fonts:
                found_font = font
                break
        
        if found_font:
            plt.rcParams['font.sans-serif'] = [found_font] + plt.rcParams['font.sans-serif']
            print(f"✅ 成功设置中文字体: {found_font}")
        else:
            print("⚠️  未找到中文字体，使用默认字体")
            # 使用英文标签作为备选方案
            return False
            
    except Exception as e:
        print(f"❌ 字体设置失败: {e}")
        return False
    
    return True

def get_labels(use_chinese=True):
    """
    获取图表标签，根据字体支持情况选择中文或英文
    """
    if use_chinese:
        return {
            'voltage': '阳极电压 U (V)',
            'current': '电流 I (A)',
            'iu_title': 'I-U特性曲线',
            'sqrt_voltage': 'sqrt(U)',
            'log_current': 'log(I)',
            'schottky_title': 'log(I)与sqrt(U)的线性关系',
            'inv_temp': '1/T (K⁻¹)',
            'log_i_t2': 'lg(I/T²)',
            'richardson_title': 'lg(I/T²)与1/T的线性关系',
            'temp_k': 'T={}K',
            'data_label': '实验数据',
            'fit_label': '拟合线: y = {:.2f}x + {:.2f}',
            'voltage_fixed': 'U={}V'
        }
    else:
        return {
            'voltage': 'Anode Voltage U (V)',
            'current': 'Current I (A)',
            'iu_title': 'I-U Characteristic Curve',
            'sqrt_voltage': 'sqrt(U)',
            'log_current': 'log(I)',
            'schottky_title': 'log(I) vs sqrt(U) Linear Relationship',
            'inv_temp': '1/T (K⁻¹)',
            'log_i_t2': 'lg(I/T²)',
            'richardson_title': 'lg(I/T²) vs 1/T Linear Relationship',
            'temp_k': 'T={}K',
            'data_label': 'Experimental Data',
            'fit_label': 'Fit: y = {:.2f}x + {:.2f}',
            'voltage_fixed': 'U={}V'
        }

def create_figure(figsize=(10, 6)):
    """
    创建matplotlib图形对象
    """
    from matplotlib.figure import Figure
    return Figure(figsize=figsize)

def save_figure_to_base64(fig):
    """
    将matplotlib图形保存为base64编码的字符串
    """
    import io
    import base64
    
    buf = io.BytesIO()
    fig.savefig(buf, format='png', dpi=100, bbox_inches='tight')
    buf.seek(0)
    img_base64 = base64.b64encode(buf.getvalue()).decode('utf-8')
    buf.close()
    
    return img_base64

# 初始化字体设置
_font_initialized = False
_use_chinese = True

def init_plotting():
    """
    初始化绘图环境
    """
    global _font_initialized, _use_chinese
    
    if not _font_initialized:
        _use_chinese = setup_chinese_font()
        _font_initialized = True
    
    return _use_chinese

def get_current_labels():
    """
    获取当前语言的标签
    """
    global _use_chinese
    return get_labels(_use_chinese)
