{% extends "base.html" %}

{% block title %}物理实验平台 - 首页{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Hero Section -->
    <div class="hero-section">
        <h1 class="display-4 fw-bold mb-4">
            <i class="fas fa-flask me-3"></i>物理实验平台
        </h1>
        <p class="lead mb-4">
            探索光电效应与热电子发射的奥秘，体验现代物理实验的魅力
        </p>
        <div class="row justify-content-center">
            <div class="col-md-8">
                <p class="mb-0">
                    本平台提供两个经典的物理实验：光电倍增管特性实验和金属电子逸出功实验。
                    通过交互式的实验界面，您可以深入理解光电效应和热电子发射的物理原理。
                </p>
            </div>
        </div>
    </div>

    <!-- Content Section -->
    <div class="content-section">
        <div class="row">
            {% for exp_key, experiment in experiments.items() %}
            <div class="col-lg-6 mb-4">
                <div class="experiment-card fade-in-up">
                    <div class="experiment-icon">
                        {% if exp_key == 'pmt' %}
                            <i class="fas fa-lightbulb"></i>
                        {% else %}
                            <i class="fas fa-fire"></i>
                        {% endif %}
                    </div>
                    
                    <h3 class="text-center mb-3">{{ experiment.name }}</h3>
                    
                    <p class="text-muted text-center mb-4">
                        {{ experiment.description }}
                    </p>
                    
                    <div class="mb-4">
                        <h5 class="mb-3">
                            <i class="fas fa-star me-2"></i>实验特色
                        </h5>
                        <ul class="feature-list">
                            {% for feature in experiment.features %}
                            <li>
                                <i class="fas fa-check-circle"></i>{{ feature }}
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ experiment.url }}" class="btn btn-custom me-2">
                            <i class="fas fa-play me-2"></i>开始实验
                        </a>
                        {% if exp_key == 'pmt' %}
                        <a href="{{ url_for('pmt_principle') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-book me-2"></i>实验原理
                        </a>
                        {% else %}
                        <a href="{{ url_for('work_function_theory') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-book me-2"></i>实验原理
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- 平台特色 -->
        <div class="row mt-5">
            <div class="col-12">
                <h2 class="text-center mb-5">
                    <i class="fas fa-gem me-2"></i>平台特色
                </h2>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card-custom h-100">
                    <div class="card-body text-center p-4">
                        <div class="experiment-icon mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h5>实时数据分析</h5>
                        <p class="text-muted">
                            实时处理实验数据，生成专业的分析图表和计算结果
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card-custom h-100">
                    <div class="card-body text-center p-4">
                        <div class="experiment-icon mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h5>响应式设计</h5>
                        <p class="text-muted">
                            支持桌面端和移动端访问，随时随地进行物理实验
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card-custom h-100">
                    <div class="card-body text-center p-4">
                        <div class="experiment-icon mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h5>教学友好</h5>
                        <p class="text-muted">
                            详细的实验原理说明和步骤指导，适合教学和自学
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 快速开始 -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card-custom">
                    <div class="card-body text-center p-5">
                        <h3 class="mb-4">
                            <i class="fas fa-rocket me-2"></i>快速开始
                        </h3>
                        <p class="lead mb-4">
                            选择一个实验开始您的物理探索之旅
                        </p>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <a href="{{ url_for('pmt_index') }}" class="btn btn-custom">
                                <i class="fas fa-lightbulb me-2"></i>PMT实验
                            </a>
                            <a href="{{ url_for('work_function_index') }}" class="btn btn-secondary-custom">
                                <i class="fas fa-fire me-2"></i>逸出功实验
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 添加滚动动画效果
document.addEventListener('DOMContentLoaded', function() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // 观察所有需要动画的元素
    document.querySelectorAll('.fade-in-up').forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = `all 0.8s ease-out ${index * 0.2}s`;
        observer.observe(el);
    });
});
</script>
{% endblock %}
