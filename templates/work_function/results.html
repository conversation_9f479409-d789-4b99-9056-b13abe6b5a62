<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实验结果 - 金属电子逸出功实验</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 1rem auto;
            padding: 0;
            overflow: hidden;
            backdrop-filter: blur(10px);
            max-width: 100%;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 1rem;
            text-align: center;
        }
        .content-section {
            padding: 2rem 1rem;
        }
        .result-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            transition: all 0.3s ease;
        }
        .result-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .chart-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }
        .highlight-result {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            border-left: 5px solid #e17055;
            text-align: center;
        }
        .work-function-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2d3436;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .btn-custom {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.6);
            color: white;
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #28a745;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
            color: white;
        }
        .step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        .table-custom {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table-custom th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
        }
        .summary-stats {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }
        .stat-item {
            text-align: center;
            padding: 1rem;
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2d3436;
        }
        .stat-label {
            color: #636e72;
            font-size: 0.9rem;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .main-container {
                margin: 0.5rem;
                border-radius: 15px;
            }
            .hero-section {
                padding: 1.5rem 1rem;
            }
            .hero-section h1 {
                font-size: 1.5rem;
            }
            .content-section {
                padding: 1.5rem 1rem;
            }
            .result-card {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }
            .highlight-result {
                padding: 1.5rem;
                margin: 1.5rem 0;
            }
            .work-function-value {
                font-size: 2rem;
            }
            .summary-stats {
                padding: 1.5rem;
                margin: 1.5rem 0;
            }
            .stat-item {
                padding: 0.75rem;
            }
            .stat-value {
                font-size: 1.2rem;
            }
            .chart-container {
                padding: 0.75rem;
                margin: 0.75rem 0;
            }
            .chart-container img {
                max-width: 100%;
                height: auto;
            }
            .btn-custom {
                padding: 0.75rem 1.5rem;
                font-size: 0.9rem;
                margin: 0.25rem;
            }
            .step-indicator {
                margin: 1rem 0;
            }
            .step {
                width: 30px;
                height: 30px;
                font-size: 0.8rem;
                margin: 0 0.25rem;
            }
            .table-custom {
                font-size: 0.9rem;
            }
            .table-custom th, .table-custom td {
                padding: 0.5rem;
                font-size: 0.8rem;
            }
            .table-responsive {
                overflow-x: auto;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .nav-link {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .hero-section h1 {
                font-size: 1.3rem;
            }
            .result-card {
                padding: 1rem;
            }
            .highlight-result {
                padding: 1rem;
            }
            .work-function-value {
                font-size: 1.5rem;
            }
            .summary-stats {
                padding: 1rem;
            }
            .stat-item {
                padding: 0.5rem;
            }
            .stat-value {
                font-size: 1rem;
            }
            .stat-label {
                font-size: 0.8rem;
            }
            .btn-custom {
                padding: 0.6rem 1.2rem;
                font-size: 0.8rem;
            }
            .step {
                width: 25px;
                height: 25px;
                font-size: 0.7rem;
            }
            .table-custom th, .table-custom td {
                padding: 0.4rem;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container-fluid px-3">
            <a class="navbar-brand fw-bold" href="{{ url_for('work_function_index') }}">
                <i class="fas fa-atom me-2"></i>电子逸出功实验
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('work_function_index') }}">首页</a>
                    <a class="nav-link" href="{{ url_for('work_function_theory') }}">实验原理</a>
                    <a class="nav-link" href="{{ url_for('work_function_steps') }}">实验步骤</a>
                    <a class="nav-link" href="{{ url_for('work_function_reset') }}">重新开始</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid" style="margin-top: 76px;">
        <div class="main-container">
            <!-- Hero Section -->
            <div class="hero-section">
                <h1><i class="fas fa-chart-line me-3"></i>实验结果</h1>
                <p class="lead">金属电子逸出功计算完成</p>
                <div class="step-indicator">
                    <div class="step"><i class="fas fa-check"></i></div>
                    <div class="step"><i class="fas fa-check"></i></div>
                    <div class="step"><i class="fas fa-check"></i></div>
                    <div class="step active">4</div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <div class="row">
                    <div class="col-lg-12">
                        
                        <!-- 主要结果 -->
                        <div class="highlight-result fade-in-up">
                            <h2><i class="fas fa-trophy me-2"></i>电子逸出功计算结果</h2>
                            <div class="work-function-value">
                                φ = {{ results.work_function }} eV
                            </div>
                            <p class="mt-3 mb-0">
                                基于Richardson-Dushman方程，通过线性拟合计算得出的金属电子逸出功
                            </p>
                        </div>

                        <!-- 统计摘要 -->
                        <div class="summary-stats fade-in-up">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ results.temp_count }}</div>
                                        <div class="stat-label">测量温度数</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ results.voltage_count }}</div>
                                        <div class="stat-label">测量电压数</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ results.r_squared2 }}</div>
                                        <div class="stat-label">拟合相关系数R²</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ (results.temp_count * results.voltage_count) }}</div>
                                        <div class="stat-label">总数据点数</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- I-U特性曲线 -->
                        <div class="result-card fade-in-up">
                            <h3><i class="fas fa-chart-area me-2"></i>I-U特性曲线</h3>
                            <p>不同温度下电流随阳极电压的变化关系，展示了热电子发射的基本特性。</p>
                            <div class="chart-container">
                                <img src="data:image/png;base64,{{ results.iu_plot }}" class="img-fluid" alt="I-U特性曲线">
                            </div>
                        </div>

                        <!-- Schottky效应分析 -->
                        <div class="result-card fade-in-up">
                            <h3><i class="fas fa-chart-line me-2"></i>Schottky效应分析</h3>
                            <p>log(I)与√U的线性关系，验证了Schottky效应的存在。</p>
                            
                            <div class="table-responsive mb-3">
                                <table class="table table-custom">
                                    <thead>
                                        <tr>
                                            <th>温度 (K)</th>
                                            <th>拟合方程</th>
                                            <th>相关系数 R²</th>
                                            <th>截距</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in results.logu_fits %}
                                        <tr>
                                            <td><strong>{{ item.temp }}</strong></td>
                                            <td>y = {{ item.slope }}x + {{ item.intercept }}</td>
                                            <td>{{ item.r_squared }}</td>
                                            <td>{{ item.intercept }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="chart-container">
                                <img src="data:image/png;base64,{{ results.logu_plot }}" class="img-fluid" alt="Schottky效应分析">
                            </div>
                        </div>

                        <!-- Richardson拟合 -->
                        <div class="result-card fade-in-up">
                            <h3><i class="fas fa-calculator me-2"></i>Richardson-Dushman拟合</h3>
                            <p>通过lg(I/T²)与1/T的线性关系计算电子逸出功。</p>
                            
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ results.slope2 }}</div>
                                        <div class="stat-label">拟合斜率</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ results.intercept2 }}</div>
                                        <div class="stat-label">拟合截距</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <div class="stat-value">{{ results.r_squared2 }}</div>
                                        <div class="stat-label">相关系数 R²</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="chart-container">
                                <img src="data:image/png;base64,{{ results.temp_plot }}" class="img-fluid" alt="Richardson拟合">
                            </div>
                        </div>

                        <!-- 详细数据 -->
                        <div class="result-card fade-in-up">
                            <h3><i class="fas fa-table me-2"></i>详细实验数据</h3>
                            
                            <h5 class="mt-4">Richardson拟合数据</h5>
                            <div class="table-responsive">
                                <table class="table table-custom">
                                    <thead>
                                        <tr>
                                            <th>温度 (K)</th>
                                            <th>1/T (K⁻¹)</th>
                                            <th>电流 I (A)</th>
                                            <th>lg(I/T²)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in results.temp_data %}
                                        <tr>
                                            <td>{{ item.temp }}</td>
                                            <td>{{ item.inv_temp }}</td>
                                            <td>{{ item.current }}</td>
                                            <td>{{ item.log_i_t2 }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <h5 class="mt-4">原始测量数据</h5>
                            <div class="table-responsive">
                                <table class="table table-custom">
                                    <thead>
                                        <tr>
                                            <th>电压 (V)</th>
                                            {% for temp in results.temperatures %}
                                            <th>{{ temp }}K</th>
                                            {% endfor %}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for i in range(results.voltage_count) %}
                                        <tr>
                                            <td><strong>{{ results.voltages[i] }}</strong></td>
                                            {% for j in range(results.temp_count) %}
                                            <td>{{ "%.2e"|format(results.currents[j][i]) }}</td>
                                            {% endfor %}
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="text-center mt-4 fade-in-up">
                            <a href="{{ url_for('work_function_reset') }}" class="btn btn-custom me-3">
                                <i class="fas fa-redo me-2"></i>重新开始实验
                            </a>
                            <button onclick="window.print()" class="btn btn-outline-secondary">
                                <i class="fas fa-print me-2"></i>打印结果
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 添加滚动动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 观察所有需要动画的元素
            document.querySelectorAll('.fade-in-up').forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = `all 0.8s ease-out ${index * 0.1}s`;
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
