<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金属电子逸出功实验数据处理系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 1rem auto;
            padding: 0;
            overflow: hidden;
            backdrop-filter: blur(10px);
            max-width: 100%;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 1rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .hero-content {
            position: relative;
            z-index: 1;
        }
        .hero-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.2;
        }
        .hero-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 1.5rem;
            line-height: 1.4;
        }
        .content-section {
            padding: 2rem 1rem;
        }
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
        }
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }
        .btn-custom {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.6);
            color: white;
        }
        .intro-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            text-align: justify;
        }
        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            border-left: 5px solid #e17055;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
            color: #6c757d;
        }
        .step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .main-container {
                margin: 0.5rem;
                border-radius: 15px;
            }
            .hero-section {
                padding: 1.5rem 1rem;
            }
            .hero-title {
                font-size: 1.5rem;
            }
            .hero-subtitle {
                font-size: 0.9rem;
            }
            .content-section {
                padding: 1.5rem 1rem;
            }
            .feature-card {
                margin-bottom: 1rem;
                padding: 1.5rem;
            }
            .feature-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
                margin-bottom: 1rem;
            }
            .btn-custom {
                padding: 0.75rem 1.5rem;
                font-size: 0.9rem;
            }
            .step-indicator {
                margin: 1rem 0;
            }
            .step {
                width: 30px;
                height: 30px;
                font-size: 0.8rem;
                margin: 0 0.25rem;
            }
            .intro-text {
                font-size: 1rem;
                text-align: left;
            }
            .highlight-box {
                padding: 1.5rem;
                margin: 1.5rem 0;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .nav-link {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 1.3rem;
            }
            .hero-subtitle {
                font-size: 0.8rem;
            }
            .feature-card {
                padding: 1rem;
            }
            .btn-custom {
                padding: 0.6rem 1.2rem;
                font-size: 0.8rem;
            }
            .step {
                width: 25px;
                height: 25px;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container-fluid px-3">
            <a class="navbar-brand fw-bold" href="{{ url_for('work_function_index') }}">
                <i class="fas fa-atom me-2"></i>电子逸出功实验
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('work_function_theory') }}">实验原理</a>
                    <a class="nav-link" href="{{ url_for('work_function_steps') }}">实验步骤</a>
                    <a class="nav-link" href="{{ url_for('work_function_input', step=1) }}">开始实验</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid px-2" style="margin-top: 76px;">
        <div class="main-container">
            <!-- Hero Section -->
            <div class="hero-section">
                <div class="hero-content fade-in-up">
                    <h1 class="hero-title">
                        <i class="fas fa-microscope me-2"></i>
                        金属电子逸出功实验
                    </h1>
                    <p class="hero-subtitle">
                        基于Richardson-Dushman方程的精确测量与数据分析系统
                    </p>
                    <div class="step-indicator">
                        <div class="step active">1</div>
                        <div class="step">2</div>
                        <div class="step">3</div>
                        <div class="step">4</div>
                    </div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="intro-text fade-in-up">
                            <h2 class="text-center mb-4">实验简介</h2>
                            <p>
                                金属电子逸出功实验是现代物理学中的重要基础实验，通过测量热电子发射现象来确定金属表面的电子逸出功。
                                本实验系统采用先进的数据处理算法，基于Richardson-Dushman方程进行精确计算，为学生提供完整的实验体验。
                            </p>
                            <p>
                                实验通过改变灯丝温度和阳极电压，测量相应的发射电流，利用线性拟合方法从实验数据中提取电子逸出功的数值。
                                整个过程涉及热力学、量子力学和统计物理学的基本原理，是理解固体物理学的重要途径。
                            </p>
                        </div>

                        <div class="highlight-box fade-in-up">
                            <h4><i class="fas fa-lightbulb me-2"></i>实验特色</h4>
                            <ul class="mb-0">
                                <li>分步骤数据输入，避免界面拥挤</li>
                                <li>实时数据验证和智能补全</li>
                                <li>多种图表展示和数据分析</li>
                                <li>详细的理论背景和实验指导</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="row mt-5">
                    <div class="col-md-4 fade-in-up">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <h4>理论学习</h4>
                            <p>深入了解热电子发射的物理原理和Richardson-Dushman方程的推导过程。</p>
                            <a href="{{ url_for('work_function_theory') }}" class="btn btn-custom">学习理论</a>
                        </div>
                    </div>
                    <div class="col-md-4 fade-in-up" style="animation-delay: 0.2s;">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-list-ol"></i>
                            </div>
                            <h4>实验步骤</h4>
                            <p>详细的实验操作指南，包括设备使用、数据采集和注意事项。</p>
                            <a href="{{ url_for('work_function_steps') }}" class="btn btn-custom">查看步骤</a>
                        </div>
                    </div>
                    <div class="col-md-4 fade-in-up" style="animation-delay: 0.4s;">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <h4>数据处理</h4>
                            <p>智能化的数据输入和处理系统，自动生成分析图表和计算结果。</p>
                            <a href="{{ url_for('work_function_input', step=1) }}" class="btn btn-custom">开始实验</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 添加滚动动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 观察所有需要动画的元素
            document.querySelectorAll('.fade-in-up').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'all 0.8s ease-out';
                observer.observe(el);
            });

            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
