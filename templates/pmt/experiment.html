{% extends "base.html" %}

{% block title %}{{ experiment.title }} - PMT实验{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('pmt_index') }}">首页</a></li>
                <li class="breadcrumb-item active">{{ experiment.title }}</li>
            </ol>
        </nav>
        
        <h1 class="mb-4">
            <i class="fas fa-flask me-2"></i>{{ experiment.title }}
        </h1>
        <p class="lead text-muted">{{ experiment.description }}</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- 实验步骤 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-list-ol me-2"></i>实验步骤</h5>
            </div>
            <div class="card-body">
                <div id="experiment-steps">
                    <!-- 步骤内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>

        <!-- 数据输入表格 -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>数据输入</h5>
                <button class="btn btn-light btn-sm" onclick="addMeasurement()">
                    <i class="fas fa-plus me-1"></i>添加数据
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="data-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                {% for field in experiment.fields %}
                                <th>{{ field.label }}</th>
                                {% endfor %}
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="data-tbody">
                            <!-- 数据行将通过JavaScript动态添加 -->
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="calculateResults()">
                        <i class="fas fa-calculator me-2"></i>计算结果
                    </button>
                    <button class="btn btn-secondary ms-2" onclick="clearData()">
                        <i class="fas fa-trash me-2"></i>清空数据
                    </button>
                    <button class="btn btn-info ms-2" onclick="exportData()">
                        <i class="fas fa-download me-2"></i>导出数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 计算结果 -->
        <div class="card mb-4" id="results-card" style="display: none;">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>计算结果</h5>
            </div>
            <div class="card-body" id="results-content">
                <!-- 结果将通过JavaScript显示 -->
            </div>
        </div>

        <!-- 数据图表 -->
        <div class="card mb-4" id="chart-card" style="display: none;">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>数据曲线</h5>
            </div>
            <div class="card-body">
                <canvas id="data-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- 实验说明 -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>实验说明</h5>
            </div>
            <div class="card-body" id="experiment-info">
                <!-- 实验说明内容将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 注意事项 -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>注意事项</h5>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li>确保PMT与实验箱连接牢固</li>
                    <li>保持实验环境完全黑暗</li>
                    <li>从0V开始逐步增加电压</li>
                    <li>注意实验箱的安全操作规程</li>
                    <li>等待系统稳定后再记录数据</li>
                    <li>多次测量取平均值提高精度</li>
                </ul>
            </div>
        </div>

        <!-- 快速输入 -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="fas fa-keyboard me-2"></i>快速输入</h5>
            </div>
            <div class="card-body">
                <form id="quick-input-form">
                    {% for field in experiment.fields %}
                    <div class="mb-3">
                        <label class="form-label">{{ field.label }}</label>
                        <input type="{{ field.type }}" 
                               class="form-control" 
                               name="{{ field.name }}"
                               step="{{ field.step }}"
                               required>
                    </div>
                    {% endfor %}
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-plus me-2"></i>添加到表格
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 隐藏的数据，用于JavaScript -->
<script type="application/json" id="experiment-data">
{
    "type": "{{ experiment_type }}",
    "title": "{{ experiment.title }}",
    "fields": {{ experiment.fields | tojson }}
}
</script>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let experimentData = JSON.parse(document.getElementById('experiment-data').textContent);
let measurements = [];
let measurementCounter = 0;
let dataChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadExperimentSteps();
    loadExperimentInfo();
    setupQuickInput();
});

// 加载实验步骤
function loadExperimentSteps() {
    const stepsContainer = document.getElementById('experiment-steps');
    const steps = getExperimentSteps(experimentData.type);
    
    let stepsHtml = '<ol class="list-group list-group-numbered">';
    steps.forEach(step => {
        stepsHtml += `<li class="list-group-item">${step}</li>`;
    });
    stepsHtml += '</ol>';
    
    stepsContainer.innerHTML = stepsHtml;
}

// 加载实验说明
function loadExperimentInfo() {
    const infoContainer = document.getElementById('experiment-info');
    const info = getExperimentInfo(experimentData.type);
    infoContainer.innerHTML = info;
}

// 获取实验步骤
function getExperimentSteps(type) {
    const steps = {
        'dark_current': [
            '将PMT与实验箱正确连接',
            '确保实验环境处于完全黑暗状态',
            '打开实验箱电源，初始化系统',
            '设置工作电压为0V开始测量',
            '等待2分钟让PMT稳定',
            '记录当前电压下的暗电流值',
            '逐步增加电压（建议步长50V-100V）',
            '重复测量直到额定电压',
            '系统将自动绘制暗电流-电压关系曲线'
        ],
        'iv_characteristic': [
            '将PMT与实验箱正确连接',
            '设置标准光源照射PMT',
            '打开实验箱，设置光强为固定值',
            '从0V开始逐步增加工作电压',
            '每隔50V记录一次阳极电流值',
            '测量到额定电压为止',
            '记录完整的电压-电流数据对',
            '系统将自动绘制伏安特性曲线',
            '分析PMT的增益特性'
        ],
        'linearity': [
            '将PMT与实验箱正确连接',
            '设置PMT工作电压为最佳工作点',
            '使用实验箱的可调光源系统',
            '从最低光强开始测量',
            '逐步增加光强度（建议10个测量点）',
            '记录每个光强对应的输出电流',
            '确保测量范围在线性区域内',
            '系统将自动绘制线性关系曲线',
            '计算线性度误差和相关系数'
        ],
        'spectrum': [
            '将PMT与实验箱正确连接',
            '设置PMT工作电压为最佳工作点',
            '准备不同颜色的光源（红、橙、黄、绿、蓝、紫）',
            '保持光源强度恒定',
            '依次使用不同颜色光源照射PMT',
            '记录每种颜色光源对应的阳极电流',
            '系统将自动填入对应波长数值',
            '完成所有颜色测量后计算结果',
            '系统将自动绘制阳极电流-波长曲线',
            '分析不同波长的光谱响应特性'
        ]
    };
    return steps[type] || [];
}

// 获取实验说明
function getExperimentInfo(type) {
    const info = {
        'dark_current': `
            <p><strong>测量目的：</strong>评估PMT的本底噪声水平</p>
            <p><strong>电压范围：</strong>从0V开始逐步增加到额定电压</p>
            <p><strong>关键参数：</strong>暗电流随电压变化曲线</p>
            <p><strong>实验箱操作：</strong>确保PMT与实验箱连接正确</p>
            <p><strong>注意事项：</strong>保持完全黑暗环境</p>
        `,
        'iv_characteristic': `
            <p><strong>测量目的：</strong>确定PMT的最佳工作点</p>
            <p><strong>电压范围：</strong>从0V开始到额定电压</p>
            <p><strong>关键参数：</strong>增益随电压的变化关系</p>
            <p><strong>典型特征：</strong>指数增长关系</p>
            <p><strong>曲线分析：</strong>系统自动绘制伏安特性曲线</p>
        `,
        'linearity': `
            <p><strong>测量目的：</strong>确定PMT的线性动态范围</p>
            <p><strong>工作电压：</strong>固定在最佳工作点</p>
            <p><strong>关键参数：</strong>线性度误差小于5%</p>
            <p><strong>曲线分析：</strong>系统自动绘制线性关系曲线</p>
            <p><strong>应用意义：</strong>定量测量的基础</p>
        `,
        'spectrum': `
            <p><strong>测量目的：</strong>了解PMT的光谱响应特性</p>
            <p><strong>波长范围：</strong>300-700nm（取决于设备）</p>
            <p><strong>关键参数：</strong>峰值波长和半峰全宽</p>
            <p><strong>曲线分析：</strong>系统自动绘制光谱响应曲线</p>
            <p><strong>应用选择：</strong>根据应用选择合适波段</p>
        `
    };
    return info[type] || '';
}

// 设置快速输入表单
function setupQuickInput() {
    document.getElementById('quick-input-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(e.target);
        const measurement = {};
        
        experimentData.fields.forEach(field => {
            measurement[field.name] = formData.get(field.name);
        });
        
        addMeasurementToTable(measurement);
        e.target.reset();
    });
}

// 添加测量数据
function addMeasurement() {
    const measurement = {};
    experimentData.fields.forEach(field => {
        measurement[field.name] = '';
    });
    addMeasurementToTable(measurement);
}

// 添加测量数据到表格
function addMeasurementToTable(measurement) {
    measurementCounter++;
    measurements.push({...measurement, id: measurementCounter});
    
    const tbody = document.getElementById('data-tbody');
    const row = document.createElement('tr');
    row.id = `row-${measurementCounter}`;
    
    let rowHtml = `<td>${measurementCounter}</td>`;
    
    experimentData.fields.forEach(field => {
        if (field.type === 'select') {
            // 下拉选择框
            let selectHtml = `
                <td>
                    <select class="form-control form-control-sm"
                            onchange="updateMeasurement(${measurementCounter}, '${field.name}', this.value); handleColorChange(${measurementCounter}, this.value)">
                        <option value="">${field.placeholder}</option>
            `;
            field.options.forEach(option => {
                const selected = measurement[field.name] === option.value ? 'selected' : '';
                selectHtml += `<option value="${option.value}" ${selected}>${option.text}</option>`;
            });
            selectHtml += `
                    </select>
                </td>
            `;
            rowHtml += selectHtml;
        } else {
            // 普通输入框
            const readonly = field.readonly ? 'readonly' : '';
            rowHtml += `
                <td>
                    <input type="${field.type}"
                           class="form-control form-control-sm"
                           value="${measurement[field.name]}"
                           step="${field.step}"
                           ${readonly}
                           onchange="updateMeasurement(${measurementCounter}, '${field.name}', this.value)">
                </td>
            `;
        }
    });
    
    rowHtml += `
        <td>
            <button class="btn btn-danger btn-sm" onclick="removeMeasurement(${measurementCounter})">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    
    row.innerHTML = rowHtml;
    tbody.appendChild(row);
}

// 更新测量数据
function updateMeasurement(id, field, value) {
    const measurement = measurements.find(m => m.id === id);
    if (measurement) {
        measurement[field] = value;
        // 数据更新后自动绘制图表
        autoDrawChart();
    }
}

// 处理颜色选择变化（自动填充波长）
function handleColorChange(id, colorValue) {
    if (experimentData.type === 'spectrum' && colorValue) {
        // 颜色对应的波长
        const colorWavelengths = {
            'red': 650,
            'orange': 590,
            'yellow': 570,
            'green': 520,
            'blue': 470,
            'violet': 420
        };

        const wavelength = colorWavelengths[colorValue];
        if (wavelength) {
            // 更新测量数据中的波长
            updateMeasurement(id, 'wavelength', wavelength);

            // 更新页面上的波长输入框
            const row = document.getElementById(`row-${id}`);
            if (row) {
                const wavelengthInput = row.querySelector('input[type="number"]');
                if (wavelengthInput) {
                    wavelengthInput.value = wavelength;
                }
            }
        }
    }
}

// 自动绘制图表（当有足够数据时）
function autoDrawChart() {
    // 检查是否有足够的有效数据
    const validMeasurements = measurements.filter(m => {
        return experimentData.fields.every(field => m[field.name] && m[field.name] !== '');
    });

    // 至少需要2个数据点才绘制图表
    if (validMeasurements.length >= 2) {
        drawDataChart();
        // 显示图表卡片
        const chartCard = document.getElementById('chart-card');
        if (chartCard) {
            chartCard.style.display = 'block';
        }
    }
}

// 删除测量数据
function removeMeasurement(id) {
    measurements = measurements.filter(m => m.id !== id);
    document.getElementById(`row-${id}`).remove();
}

// 清空所有数据
function clearData() {
    if (confirm('确定要清空所有数据吗？')) {
        measurements = [];
        measurementCounter = 0;
        document.getElementById('data-tbody').innerHTML = '';
        document.getElementById('results-card').style.display = 'none';
    }
}

// 计算结果
async function calculateResults() {
    if (measurements.length === 0) {
        alert('请先输入测量数据');
        return;
    }
    
    // 验证数据完整性
    const validMeasurements = measurements.filter(m => {
        return experimentData.fields.every(field => m[field.name] && m[field.name] !== '');
    });
    
    if (validMeasurements.length === 0) {
        alert('请确保所有数据都已填写完整');
        return;
    }
    
    try {
        const response = await fetch('/pmt/calculate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                experiment_type: experimentData.type,
                measurements: validMeasurements
            })
        });
        
        const results = await response.json();
        
        if (results.error) {
            alert('计算错误: ' + results.error);
            return;
        }
        
        displayResults(results);
    } catch (error) {
        alert('计算失败: ' + error.message);
    }
}

// 显示计算结果
function displayResults(results) {
    const resultsCard = document.getElementById('results-card');
    const resultsContent = document.getElementById('results-content');

    let html = '<div class="row">';

    // 显示主要结果
    Object.entries(results).forEach(([key, value]) => {
        if (key !== 'analysis') {
            const label = getResultLabel(key);
            html += `
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-3">
                        <h6 class="text-primary mb-1">${label}</h6>
                        <p class="h5 mb-0">${value}</p>
                    </div>
                </div>
            `;
        }
    });

    html += '</div>';

    // 显示分析结果
    if (results.analysis) {
        html += `
            <div class="alert alert-info mt-3">
                <h6><i class="fas fa-chart-line me-2"></i>分析结果</h6>
                <p class="mb-0">${results.analysis}</p>
            </div>
        `;
    }

    resultsContent.innerHTML = html;
    resultsCard.style.display = 'block';

    // 绘制数据曲线
    drawDataChart();

    // 滚动到结果区域
    resultsCard.scrollIntoView({ behavior: 'smooth' });
}

// 绘制数据曲线
function drawDataChart() {
    console.log('drawDataChart called, measurements count:', measurements.length);

    if (measurements.length < 2) {
        console.log('Not enough measurements for chart, need at least 2');
        return; // 至少需要2个数据点才能绘制曲线
    }

    const chartCard = document.getElementById('chart-card');
    const ctx = document.getElementById('data-chart').getContext('2d');

    if (!ctx) {
        console.error('Cannot get canvas context for data-chart');
        return;
    }

    // 销毁现有图表
    if (dataChart) {
        console.log('Destroying existing chart');
        dataChart.destroy();
    }

    // 准备数据
    console.log('Preparing chart data...');
    const chartData = prepareChartData();

    if (!chartData || !chartData.data || !chartData.data.datasets || chartData.data.datasets.length === 0) {
        console.error('Invalid chart data:', chartData);
        return;
    }

    // 创建新图表
    console.log('Creating new chart with data:', chartData);

    try {
        dataChart = new Chart(ctx, {
        type: 'line',
        data: chartData.data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: chartData.title,
                    font: {
                        size: 16
                    }
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                x: {
                    type: 'linear',
                    display: true,
                    title: {
                        display: true,
                        text: chartData.xLabel,
                        font: {
                            size: 14
                        }
                    },
                    grid: {
                        display: true
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    title: {
                        display: true,
                        text: chartData.yLabel,
                        font: {
                            size: 14
                        }
                    },
                    grid: {
                        display: true
                    }
                }
            },
            elements: {
                point: {
                    radius: 6,
                    hoverRadius: 10,
                    backgroundColor: 'white',
                    borderWidth: 2
                },
                line: {
                    borderWidth: 3,
                    tension: 0.1
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    console.log('Chart created successfully');
    chartCard.style.display = 'block';

    } catch (error) {
        console.error('Error creating chart:', error);
        console.error('Chart data was:', chartData);

        // 显示错误信息给用户
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger';
        errorDiv.innerHTML = `<strong>图表创建失败:</strong> ${error.message}`;
        chartCard.querySelector('.card-body').appendChild(errorDiv);
        chartCard.style.display = 'block';
    }
}

// 准备图表数据
function prepareChartData() {
    const validMeasurements = measurements.filter(m => {
        const isValid = experimentData.fields.every(field => {
            const value = m[field.name];
            return value !== undefined && value !== null && value !== '';
        });
        console.log('Measurement validation:', m, 'isValid:', isValid);
        return isValid;
    });

    console.log('Valid measurements:', validMeasurements);
    console.log('Experiment type:', experimentData.type);

    if (validMeasurements.length === 0) {
        console.warn('No valid measurements found');
        return null;
    }

    let data, title, xLabel, yLabel, borderColor, backgroundColor;

    switch (experimentData.type) {
        case 'dark_current':
            data = validMeasurements.map(m => {
                const x = parseFloat(m.voltage);
                const y = parseFloat(m.current);
                console.log('Dark current data point:', {voltage: m.voltage, current: m.current, x, y});
                if (isNaN(x) || isNaN(y)) {
                    console.warn('Invalid data point:', m);
                }
                return {x, y};
            }).filter(point => !isNaN(point.x) && !isNaN(point.y));
            title = '暗电流-电压特性曲线';
            xLabel = '工作电压 (V)';
            yLabel = '暗电流 (nA)';
            borderColor = 'rgb(255, 193, 7)';
            backgroundColor = 'rgba(255, 193, 7, 0.2)';
            break;

        case 'iv_characteristic':
            data = validMeasurements.map(m => {
                const x = parseFloat(m.voltage);
                const y = parseFloat(m.current);
                console.log('IV characteristic data point:', {voltage: m.voltage, current: m.current, x, y});
                if (isNaN(x) || isNaN(y)) {
                    console.warn('Invalid data point:', m);
                }
                return {x, y};
            }).filter(point => !isNaN(point.x) && !isNaN(point.y));
            title = '伏安特性曲线';
            xLabel = '工作电压 (V)';
            yLabel = '阳极电流 (μA)';
            borderColor = 'rgb(40, 167, 69)';
            backgroundColor = 'rgba(40, 167, 69, 0.2)';
            break;

        case 'linearity':
            data = validMeasurements.map(m => {
                const x = parseFloat(m.light_intensity);
                const y = parseFloat(m.output_current);
                console.log('Linearity data point:', {light_intensity: m.light_intensity, output_current: m.output_current, x, y});
                if (isNaN(x) || isNaN(y)) {
                    console.warn('Invalid data point:', m);
                }
                return {x, y};
            }).filter(point => !isNaN(point.x) && !isNaN(point.y));
            title = '线性关系曲线';
            xLabel = '光强 (相对单位)';
            yLabel = '输出电流 (μA)';
            borderColor = 'rgb(23, 162, 184)';
            backgroundColor = 'rgba(23, 162, 184, 0.2)';
            break;

        case 'spectrum':
            data = validMeasurements.map(m => {
                const x = parseFloat(m.wavelength);
                const y = parseFloat(m.response);
                console.log('Spectrum data point:', {wavelength: m.wavelength, response: m.response, x, y});
                if (isNaN(x) || isNaN(y)) {
                    console.warn('Invalid data point:', m);
                }
                return {x, y};
            }).filter(point => !isNaN(point.x) && !isNaN(point.y));
            title = '光谱响应曲线';
            xLabel = '波长 (nm)';
            yLabel = '相对响应度';
            borderColor = 'rgb(220, 53, 69)';
            backgroundColor = 'rgba(220, 53, 69, 0.2)';
            break;

        default:
            data = [];
            title = '数据曲线';
            xLabel = 'X轴';
            yLabel = 'Y轴';
            borderColor = 'rgb(108, 117, 125)';
            backgroundColor = 'rgba(108, 117, 125, 0.2)';
    }

    // 按X轴排序
    data.sort((a, b) => a.x - b.x);

    console.log('Chart data points:', data);
    console.log('X values:', data.map(d => d.x));
    console.log('Y values:', data.map(d => d.y));

    return {
        data: {
            datasets: [{
                label: title,
                data: data,
                borderColor: borderColor,
                backgroundColor: backgroundColor,
                fill: false
            }]
        },
        title: title,
        xLabel: xLabel,
        yLabel: yLabel
    };
}

// 获取结果标签
function getResultLabel(key) {
    const labels = {
        'average_dark_current': '平均暗电流 (nA)',
        'standard_deviation': '标准偏差 (nA)',
        'voltage_range': '电压范围',
        'current_range': '电流范围',
        'gain_slope': '增益斜率',
        'slope': '斜率',
        'intercept': '截距',
        'correlation_coefficient': '相关系数',
        'linearity_error': '线性度误差 (%)',
        'peak_wavelength': '峰值波长 (nm)',
        'peak_response': '峰值响应',
        'peak_current': '峰值电流 (μA)',
        'fwhm': '半峰全宽 (nm)',
        'wavelength_range': '波长范围',
        'avg_relative_response': '平均相对响应',
        'colors_tested': '测试颜色',
        'measurement_count': '测量点数'
    };
    return labels[key] || key;
}

// 导出数据
function exportData() {
    if (measurements.length === 0) {
        alert('没有数据可导出');
        return;
    }
    
    const data = {
        experiment_type: experimentData.type,
        experiment_title: experimentData.title,
        timestamp: new Date().toISOString(),
        measurements: measurements
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `PMT_${experimentData.type}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
</script>
{% endblock %}
