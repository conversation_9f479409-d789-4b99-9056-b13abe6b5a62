<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表测试页面</title>
    <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <style>
        .chart-container { height: 400px; margin: 20px 0; }
        .test-data { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>图表功能测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>测试数据</h3>
                <div class="test-data">
                    <h5>暗电流测试数据</h5>
                    <pre id="dark-current-data"></pre>
                </div>
                <button class="btn btn-primary" onclick="testDarkCurrent()">测试暗电流图表</button>
            </div>
            <div class="col-md-6">
                <h3>图表显示</h3>
                <div class="chart-container">
                    <canvas id="testChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>调试信息</h3>
                <div id="debug-info" class="alert alert-info"></div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='vendor/chartjs/chart.min.js') }}"></script>
    <script>
        let testChart = null;
        
        // 测试数据
        const testData = {
            darkCurrent: [
                {voltage: '0', current: '1.2'},
                {voltage: '100', current: '3.5'},
                {voltage: '200', current: '8.1'},
                {voltage: '300', current: '15.6'},
                {voltage: '400', current: '28.3'},
                {voltage: '500', current: '45.7'}
            ]
        };
        
        function updateDebugInfo(message) {
            document.getElementById('debug-info').innerHTML += '<p>' + message + '</p>';
        }
        
        function testDarkCurrent() {
            updateDebugInfo('开始测试暗电流图表...');
            
            // 显示测试数据
            document.getElementById('dark-current-data').textContent = JSON.stringify(testData.darkCurrent, null, 2);
            
            // 准备图表数据
            const data = testData.darkCurrent.map(m => ({
                x: parseFloat(m.voltage),
                y: parseFloat(m.current)
            }));
            
            updateDebugInfo('处理后的数据点: ' + JSON.stringify(data));
            updateDebugInfo('X值: ' + data.map(d => d.x).join(', '));
            updateDebugInfo('Y值: ' + data.map(d => d.y).join(', '));
            
            // 销毁现有图表
            if (testChart) {
                testChart.destroy();
            }
            
            const ctx = document.getElementById('testChart').getContext('2d');
            
            try {
                testChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        datasets: [{
                            label: '暗电流-电压特性曲线',
                            data: data,
                            borderColor: 'rgb(255, 193, 7)',
                            backgroundColor: 'rgba(255, 193, 7, 0.2)',
                            fill: false,
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '暗电流-电压特性曲线',
                                font: {
                                    size: 16
                                }
                            },
                            legend: {
                                display: true,
                                position: 'top'
                            }
                        },
                        scales: {
                            x: {
                                type: 'linear',
                                display: true,
                                title: {
                                    display: true,
                                    text: '工作电压 (V)',
                                    font: {
                                        size: 14
                                    }
                                },
                                grid: {
                                    display: true
                                }
                            },
                            y: {
                                type: 'linear',
                                display: true,
                                title: {
                                    display: true,
                                    text: '暗电流 (nA)',
                                    font: {
                                        size: 14
                                    }
                                },
                                grid: {
                                    display: true
                                }
                            }
                        },
                        elements: {
                            point: {
                                radius: 6,
                                hoverRadius: 10,
                                backgroundColor: 'white',
                                borderWidth: 2
                            },
                            line: {
                                borderWidth: 3,
                                tension: 0.1
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        }
                    }
                });
                
                updateDebugInfo('✅ 图表创建成功！');
                
            } catch (error) {
                updateDebugInfo('❌ 图表创建失败: ' + error.message);
                console.error('Chart creation error:', error);
            }
        }
        
        // 页面加载时显示初始信息
        window.addEventListener('load', function() {
            updateDebugInfo('页面加载完成，Chart.js版本: ' + (Chart.version || '未知'));
            updateDebugInfo('点击"测试暗电流图表"按钮开始测试');
        });
    </script>
</body>
</html>
