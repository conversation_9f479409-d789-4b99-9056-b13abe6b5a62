# 物理实验平台

一个集成了光电倍增管(PMT)特性实验和金属电子逸出功实验的Web平台。

## 🌟 特性

- **双实验集成**: 包含PMT特性实验和金属电子逸出功实验
- **交互式界面**: 现代化的Web界面，支持桌面端和移动端
- **实时数据分析**: 自动处理实验数据并生成专业图表
- **Docker部署**: 一键部署，开箱即用
- **响应式设计**: 适配各种屏幕尺寸

## 🧪 实验内容

### 光电倍增管(PMT)特性实验
- 暗电流测量
- I-V特性曲线
- 线性度测试
- 光谱响应分析

### 金属电子逸出功实验
- Richardson-Dushman方程验证
- Schottky效应分析
- 电子逸出功计算
- 热电子发射特性研究

## 🚀 快速开始

### 使用Docker部署（推荐）

1. 克隆项目
```bash
git clone <repository-url>
cd physics_experiments
```

2. 运行部署脚本
```bash
./deploy.sh
```

3. 访问应用
打开浏览器访问: http://localhost:20050

### 手动部署

1. 安装依赖
```bash
pip install -r requirements.txt
```

2. 启动应用
```bash
python app.py
```

3. 访问应用
打开浏览器访问: http://localhost:20050

## 📁 项目结构

```
physics_experiments/
├── app.py                 # 主应用文件
├── requirements.txt       # Python依赖
├── Dockerfile            # Docker配置
├── docker-compose.yml    # Docker Compose配置
├── gunicorn.conf.py      # Gunicorn配置
├── deploy.sh             # 部署脚本
├── experiments/          # 实验模块
│   ├── pmt/             # PMT实验
│   └── work_function/   # 逸出功实验
├── templates/           # 模板文件
│   ├── base.html       # 基础模板
│   ├── index.html      # 主页
│   ├── error.html      # 错误页面
│   ├── pmt/           # PMT实验模板
│   └── work_function/ # 逸出功实验模板
└── static/             # 静态文件
    ├── css/
    ├── js/
    └── images/
```

## 🔧 配置

### 环境变量

- `FLASK_HOST`: 服务器地址 (默认: 0.0.0.0)
- `FLASK_PORT`: 服务器端口 (默认: 20050)
- `FLASK_DEBUG`: 调试模式 (默认: false)
- `FLASK_ENV`: 运行环境 (默认: production)

### Docker配置

应用默认运行在端口20050上。如需修改端口，请编辑`docker-compose.yml`文件。

## 📊 API接口

### 健康检查
```
GET /api/health
```

### 实验列表
```
GET /api/experiments
```

### PMT实验计算
```
POST /pmt/calculate
Content-Type: application/json

{
  "experiment_type": "dark_current",
  "measurements": [
    {"voltage": 1000, "current": 2.5},
    {"voltage": 1000, "current": 2.3}
  ]
}
```

## 🧪 使用说明

### PMT实验
1. 访问 `/pmt/` 进入PMT实验主页
2. 选择实验类型（暗电流、I-V特性、线性度、光谱响应）
3. 输入测量数据
4. 查看分析结果

### 逸出功实验
1. 访问 `/work_function/` 进入逸出功实验主页
2. 按步骤输入不同温度下的电流数据
3. 系统自动生成I-U特性曲线、Schottky效应分析和Richardson拟合
4. 计算得出金属的电子逸出功

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   - 检查端口20050是否被其他程序占用
   - 修改docker-compose.yml中的端口映射

2. **Docker构建失败**
   - 确保Docker和Docker Compose已正确安装
   - 检查网络连接，确保能够下载依赖包

3. **中文字体显示问题**
   - 系统会自动尝试使用中文字体
   - 如果中文显示异常，图表会使用英文标签

### 查看日志

```bash
# 查看容器日志
docker-compose logs

# 实时查看日志
docker-compose logs -f
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目采用MIT许可证。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件

---

**物理实验平台** - 让物理实验更简单、更直观！
