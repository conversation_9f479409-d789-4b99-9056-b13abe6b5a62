#!/usr/bin/env python3
"""
修复模板文件中的URL引用
"""

import os
import re

def fix_pmt_templates():
    """修复PMT模板中的URL引用"""
    pmt_template_dir = "templates/pmt"
    
    # URL映射
    url_mappings = {
        r"url_for\('principle'\)": "url_for('pmt_principle')",
        r"url_for\('experiment',\s*experiment_type=": "url_for('pmt_experiment_page', experiment_type=",
        r"url_for\('index'\)": "url_for('pmt_index')",
        r"url_for\('calculate'\)": "url_for('pmt_calculate')",
    }
    
    print("🔧 修复PMT模板...")
    
    for root, dirs, files in os.walk(pmt_template_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                print(f"  处理: {file_path}")
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                for pattern, replacement in url_mappings.items():
                    content = re.sub(pattern, replacement, content)
                
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"    ✅ 已修复")
                else:
                    print(f"    ⏭️  无需修复")

def fix_work_function_templates():
    """修复Work Function模板中的URL引用"""
    wf_template_dir = "templates/work_function"
    
    # URL映射
    url_mappings = {
        r"url_for\('theory'\)": "url_for('work_function_theory')",
        r"url_for\('steps'\)": "url_for('work_function_steps')",
        r"url_for\('index'\)": "url_for('work_function_index')",
        r"url_for\('input_data',\s*step=": "url_for('work_function_input', step=",
        r"url_for\('save_data',\s*step=": "url_for('work_function_save_data', step=",
        r"url_for\('results'\)": "url_for('work_function_results')",
        r"url_for\('reset'\)": "url_for('work_function_reset')",
    }
    
    print("🔧 修复Work Function模板...")
    
    for root, dirs, files in os.walk(wf_template_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                print(f"  处理: {file_path}")
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                for pattern, replacement in url_mappings.items():
                    content = re.sub(pattern, replacement, content)
                
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"    ✅ 已修复")
                else:
                    print(f"    ⏭️  无需修复")

def main():
    print("🚀 开始修复模板文件中的URL引用")
    print("=" * 50)
    
    # 修复PMT模板
    fix_pmt_templates()
    
    print()
    
    # 修复Work Function模板
    fix_work_function_templates()
    
    print()
    print("=" * 50)
    print("🎉 模板修复完成！")

if __name__ == "__main__":
    main()
