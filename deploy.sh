#!/bin/bash

# 物理实验平台部署脚本

set -e

echo "🚀 开始部署物理实验平台..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down || true

# 清理旧镜像（可选）
if [ "$1" = "--clean" ]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
    docker rmi physics-experiments_physics-experiments || true
fi

# 构建新镜像
echo "🔨 构建Docker镜像..."
docker-compose build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -f http://localhost:20050/api/health > /dev/null 2>&1; then
    echo "✅ 服务启动成功！"
    echo "📱 访问地址: http://localhost:20050"
    echo "🔍 健康检查: http://localhost:20050/api/health"
    echo "📊 实验列表: http://localhost:20050/api/experiments"
else
    echo "❌ 服务启动失败，请检查日志"
    docker-compose logs
    exit 1
fi

# 显示容器状态
echo ""
echo "📋 容器状态:"
docker-compose ps

echo ""
echo "🎉 部署完成！"
echo "📱 请访问: http://localhost:20050"
