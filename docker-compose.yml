version: '3.8'

services:
  physics-experiments:
    build: .
    container_name: physics-experiments
    ports:
      - "20050:20050"
    environment:
      - FLASK_ENV=production
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=20050
      - FLASK_DEBUG=false
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:20050/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - physics-net

networks:
  physics-net:
    driver: bridge
