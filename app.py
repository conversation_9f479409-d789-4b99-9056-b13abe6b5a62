from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import numpy as np
import json
import os
from datetime import datetime

# 导入实验模块
from experiments.pmt.pmt_experiment import PMTExperiment
from experiments.work_function.work_function_experiment import WorkFunctionExperiment

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'physics-experiments-secret-key-2024'

# 配置
app.config.update({
    'DEBUG': os.environ.get('FLASK_DEBUG', 'False').lower() == 'true',
    'HOST': os.environ.get('FLASK_HOST', '0.0.0.0'),
    'PORT': int(os.environ.get('FLASK_PORT', 20050))
})

# 初始化实验模块
pmt_experiment = PMTExperiment()
work_function_experiment = WorkFunctionExperiment()

@app.route('/')
def index():
    """主页 - 实验选择界面"""
    experiments = {
        'pmt': {
            'name': '光电倍增管特性实验',
            'description': '研究光电倍增管的暗电流、I-V特性、线性度和光谱响应等特性',
            'image': 'pmt_icon.png',
            'url': '/pmt/',
            'features': ['暗电流测量', 'I-V特性曲线', '线性度分析', '光谱响应测试']
        },
        'work_function': {
            'name': '金属电子逸出功实验',
            'description': '通过热电子发射现象测量金属的电子逸出功',
            'image': 'work_function_icon.png',
            'url': '/work_function/',
            'features': ['Richardson-Dushman方程', 'Schottky效应分析', '逸出功计算', '数据拟合']
        }
    }
    return render_template('index.html', experiments=experiments)

# PMT实验路由
@app.route('/pmt/')
def pmt_index():
    """PMT实验主页"""
    return pmt_experiment.index()

@app.route('/pmt/principle')
def pmt_principle():
    """PMT实验原理"""
    return pmt_experiment.principle()

@app.route('/pmt/experiment/<experiment_type>')
def pmt_experiment_page(experiment_type):
    """PMT实验页面"""
    return pmt_experiment.experiment_page(experiment_type)

@app.route('/pmt/calculate', methods=['POST'])
def pmt_calculate():
    """PMT实验计算"""
    return pmt_experiment.calculate(request.json)

# Work Function实验路由
@app.route('/work_function/')
def work_function_index():
    """逸出功实验主页"""
    return work_function_experiment.index()

@app.route('/work_function/theory')
def work_function_theory():
    """逸出功实验原理"""
    return work_function_experiment.theory()

@app.route('/work_function/steps')
def work_function_steps():
    """逸出功实验步骤"""
    return work_function_experiment.steps()

@app.route('/work_function/input/<int:step>')
def work_function_input(step):
    """逸出功实验数据输入"""
    return work_function_experiment.input_data(step)

@app.route('/work_function/save_data/<int:step>', methods=['POST'])
def work_function_save_data(step):
    """保存逸出功实验数据"""
    return work_function_experiment.save_data(step, request.form, session)

@app.route('/work_function/results')
def work_function_results():
    """逸出功实验结果"""
    return work_function_experiment.results(session)

@app.route('/work_function/reset')
def work_function_reset():
    """重置逸出功实验"""
    return work_function_experiment.reset(session)

# API路由
@app.route('/api/experiments')
def api_experiments():
    """获取实验列表API"""
    experiments = ['pmt', 'work_function']
    return jsonify({'experiments': experiments})

@app.route('/api/health')
def api_health():
    """健康检查API"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'experiments': ['pmt', 'work_function']
    })

# 错误处理
@app.errorhandler(404)
def not_found_error(error):
    return render_template('error.html', 
                         error_code=404, 
                         error_message="页面未找到"), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', 
                         error_code=500, 
                         error_message="服务器内部错误"), 500

if __name__ == '__main__':
    app.run(
        debug=app.config['DEBUG'],
        host=app.config['HOST'],
        port=app.config['PORT']
    )
