#!/usr/bin/env python3
"""
测试合并后的物理实验应用
"""

import requests
import json
import time

def test_main_pages():
    """测试主要页面"""
    base_url = "http://127.0.0.1:20051"
    
    pages = [
        "/",
        "/pmt/",
        "/pmt/principle",
        "/work_function/",
        "/work_function/theory",
        "/work_function/steps",
        "/api/health",
        "/api/experiments"
    ]
    
    print("🧪 测试主要页面...")
    
    for page in pages:
        try:
            response = requests.get(f"{base_url}{page}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {page}: 正常")
            else:
                print(f"❌ {page}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {page}: 连接失败 - {e}")

def test_pmt_calculation():
    """测试PMT实验计算功能"""
    base_url = "http://127.0.0.1:20051"
    
    print("\n🧪 测试PMT实验计算...")
    
    # 测试暗电流计算
    dark_current_data = {
        "experiment_type": "dark_current",
        "measurements": [
            {"voltage": 1000, "current": 2.5},
            {"voltage": 1000, "current": 2.3},
            {"voltage": 1000, "current": 2.7},
            {"voltage": 1000, "current": 2.4}
        ]
    }
    
    try:
        response = requests.post(f"{base_url}/pmt/calculate", 
                               json=dark_current_data, 
                               timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ PMT暗电流计算: 平均值 {result.get('average_dark_current')} nA")
        else:
            print(f"❌ PMT暗电流计算失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ PMT暗电流计算失败: {e}")

def test_work_function_flow():
    """测试逸出功实验流程"""
    base_url = "http://127.0.0.1:20051"
    
    print("\n🧪 测试逸出功实验流程...")
    
    # 测试数据输入页面
    for step in range(1, 6):
        try:
            response = requests.get(f"{base_url}/work_function/input/{step}", timeout=5)
            if response.status_code == 200:
                print(f"✅ 逸出功实验步骤{step}: 正常")
            else:
                print(f"❌ 逸出功实验步骤{step}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 逸出功实验步骤{step}: {e}")

def test_plotting_functionality():
    """测试绘图功能"""
    print("\n🧪 测试绘图功能...")
    
    try:
        # 导入绘图模块进行测试
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        
        from experiments.work_function.plotting_utils import init_plotting, create_figure, save_figure_to_base64
        import numpy as np
        
        # 初始化绘图
        use_chinese = init_plotting()
        print(f"✅ 绘图初始化完成，中文支持: {'是' if use_chinese else '否'}")
        
        # 创建测试图表
        fig = create_figure()
        ax = fig.add_subplot(111)
        
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y, 'b-', label='Test')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_title('Test Plot')
        ax.legend()
        ax.grid(True)
        
        # 转换为base64
        img_base64 = save_figure_to_base64(fig)
        print(f"✅ 图表生成成功，base64长度: {len(img_base64)}")
        
    except Exception as e:
        print(f"❌ 绘图功能测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 开始测试合并后的物理实验应用")
    print("=" * 60)
    
    # 等待应用启动
    print("⏳ 等待应用启动...")
    time.sleep(2)
    
    # 测试主要页面
    test_main_pages()
    
    # 测试PMT计算功能
    test_pmt_calculation()
    
    # 测试逸出功实验流程
    test_work_function_flow()
    
    # 测试绘图功能
    test_plotting_functionality()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("📱 请访问: http://127.0.0.1:20051")

if __name__ == "__main__":
    main()
