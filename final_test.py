#!/usr/bin/env python3
"""
最终系统测试脚本
"""

import requests
import json
import time
import sys

def test_system_comprehensive():
    """全面测试系统功能"""
    base_url = "http://127.0.0.1:20050"
    
    print("🚀 开始全面系统测试...")
    print("=" * 60)
    
    # 1. 测试主页和基本页面
    print("📄 测试基本页面...")
    pages = [
        ("/", "主页"),
        ("/pmt/", "PMT实验主页"),
        ("/pmt/principle", "PMT实验原理"),
        ("/work_function/", "逸出功实验主页"),
        ("/work_function/theory", "逸出功实验原理"),
        ("/work_function/steps", "逸出功实验步骤"),
        ("/api/health", "健康检查API"),
        ("/api/experiments", "实验列表API")
    ]
    
    for path, name in pages:
        try:
            response = requests.get(f"{base_url}{path}", timeout=10)
            if response.status_code == 200:
                print(f"  ✅ {name}: 正常")
            else:
                print(f"  ❌ {name}: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ {name}: 连接失败 - {e}")
            return False
    
    # 2. 测试PMT实验的所有计算功能
    print("\n🧪 测试PMT实验计算功能...")
    
    pmt_tests = [
        {
            "name": "暗电流测量",
            "data": {
                "experiment_type": "dark_current",
                "measurements": [
                    {"voltage": 1000, "current": 2.5},
                    {"voltage": 1000, "current": 2.3},
                    {"voltage": 1000, "current": 2.7}
                ]
            }
        },
        {
            "name": "I-V特性测量",
            "data": {
                "experiment_type": "iv_characteristic",
                "measurements": [
                    {"voltage": 800, "current": 1.2},
                    {"voltage": 1000, "current": 5.8},
                    {"voltage": 1200, "current": 25.6}
                ]
            }
        },
        {
            "name": "线性度测试",
            "data": {
                "experiment_type": "linearity",
                "measurements": [
                    {"light_intensity": 1.0, "output_current": 10.2},
                    {"light_intensity": 2.0, "output_current": 20.1},
                    {"light_intensity": 3.0, "output_current": 30.5}
                ]
            }
        },
        {
            "name": "光谱响应测试",
            "data": {
                "experiment_type": "spectrum",
                "measurements": [
                    {"wavelength": 400, "current": 5.2, "color": "蓝色"},
                    {"wavelength": 550, "current": 15.8, "color": "绿色"},
                    {"wavelength": 650, "current": 8.3, "color": "红色"}
                ]
            }
        }
    ]
    
    for test in pmt_tests:
        try:
            response = requests.post(f"{base_url}/pmt/calculate", 
                                   json=test["data"], 
                                   timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"  ✅ {test['name']}: 计算成功")
            else:
                print(f"  ❌ {test['name']}: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ {test['name']}: 计算失败 - {e}")
            return False
    
    # 3. 测试逸出功实验流程
    print("\n🔥 测试逸出功实验流程...")
    
    # 测试所有步骤的输入页面
    for step in range(1, 6):
        try:
            response = requests.get(f"{base_url}/work_function/input/{step}", timeout=10)
            if response.status_code == 200:
                print(f"  ✅ 步骤{step}输入页面: 正常")
            else:
                print(f"  ❌ 步骤{step}输入页面: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ 步骤{step}输入页面: 失败 - {e}")
            return False
    
    # 4. 测试绘图功能
    print("\n📊 测试绘图功能...")
    try:
        # 导入绘图模块进行测试
        sys.path.append('.')
        from experiments.work_function.plotting_utils import (
            init_plotting, create_figure, save_figure_to_base64, get_current_labels
        )
        import numpy as np
        
        # 初始化绘图
        use_chinese = init_plotting()
        print(f"  ✅ 绘图环境初始化: {'支持中文' if use_chinese else '使用英文'}")
        
        # 测试图表生成
        fig = create_figure()
        ax = fig.add_subplot(111)
        
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y, 'b-', label='Test')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_title('Test Plot')
        ax.legend()
        ax.grid(True)
        
        # 转换为base64
        img_base64 = save_figure_to_base64(fig)
        print(f"  ✅ 图表生成: 成功 (长度: {len(img_base64)})")
        
        # 测试标签获取
        labels = get_current_labels()
        print(f"  ✅ 标签系统: 正常 (共{len(labels)}个标签)")
        
    except Exception as e:
        print(f"  ❌ 绘图功能测试失败: {e}")
        return False
    
    # 5. 测试API响应
    print("\n🔌 测试API响应...")
    try:
        # 健康检查
        health_response = requests.get(f"{base_url}/api/health", timeout=5)
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"  ✅ 健康检查: {health_data.get('status', 'unknown')}")
        else:
            print(f"  ❌ 健康检查失败: HTTP {health_response.status_code}")
            return False
        
        # 实验列表
        experiments_response = requests.get(f"{base_url}/api/experiments", timeout=5)
        if experiments_response.status_code == 200:
            experiments_data = experiments_response.json()
            print(f"  ✅ 实验列表: {len(experiments_data.get('experiments', []))}个实验")
        else:
            print(f"  ❌ 实验列表失败: HTTP {experiments_response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ API测试失败: {e}")
        return False
    
    return True

def main():
    print("🎯 物理实验平台 - 最终系统测试")
    print("=" * 60)
    
    # 等待系统启动
    print("⏳ 等待系统启动...")
    time.sleep(3)
    
    # 执行全面测试
    if test_system_comprehensive():
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！系统运行正常")
        print("📱 访问地址: http://localhost:20050")
        print("🔍 健康检查: http://localhost:20050/api/health")
        print("📊 PMT实验: http://localhost:20050/pmt/")
        print("🔥 逸出功实验: http://localhost:20050/work_function/")
        print("=" * 60)
        return 0
    else:
        print("\n" + "=" * 60)
        print("❌ 部分测试失败，请检查系统状态")
        print("=" * 60)
        return 1

if __name__ == "__main__":
    sys.exit(main())
