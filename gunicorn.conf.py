# Gunicorn配置文件

import os

# 服务器套接字
bind = f"0.0.0.0:{os.environ.get('FLASK_PORT', 20050)}"
backlog = 2048

# 工作进程
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 120
keepalive = 2

# 重启
max_requests = 1000
max_requests_jitter = 50
preload_app = True

# 日志
accesslog = "-"
errorlog = "-"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# 进程命名
proc_name = "physics-experiments"

# 用户和组
user = "appuser"
group = "appuser"

# 临时目录
tmp_upload_dir = None

# 安全
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190
